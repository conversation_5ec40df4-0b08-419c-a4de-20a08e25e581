from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    ChatPromptTemplate
from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>
from langchain_core.runnables.history import RunnableWithMessageHistory

from Configs.Config import SysConfig
from LLM.LLMManager import sys_llm_manager
from Models.agent.TalentInfoProcessing import ContentInfo
from Utils.CommonUtils import remove_think_tags
from Utils.logs.LoggingConfig import logger


class ImproveInfoAgent:
    _llm_name: str | None = "DEFAULT"

    def __init__(self):
        self.__temperature = SysConfig["agents"]["kb_agent"]["temperature"]
        self.__output_improve_info = PydanticOutputParser(pydantic_object=ContentInfo)

    # 根据LLM的名字设置LLM相关信息，包含对话信息
    def __set_llm(self, output_parser: PydanticOutputParser, system_template: SystemMessagePromptTemplate,
                  human_template: HumanMessagePromptTemplate) -> RunnableWithMessageHistory | None:
        llm_helper = sys_llm_manager.get_generate_use_llm_helper()
        if llm_helper is None:
            return None
        llm_obj = llm_helper.get_llm_object(self.__temperature)

        prompt_template = ChatPromptTemplate.from_messages([
            system_template,
            human_template
        ]).partial(format_instructions=output_parser.get_format_instructions())

        runnable = (prompt_template | llm_obj
                    | RunnableLambda(lambda x: x if isinstance(x, str) else x.content)
                    | RunnableLambda(remove_think_tags)
                    | output_parser)
        return runnable

    # 一键完善
    async def evaluateImproveInfo(self, evaluate):
        system_template = SystemMessagePromptTemplate.from_template(
            """
            - Role: 专业面试评价优化专家
            - Background: 用户已经得到了一份面试官对面试结果的评价，但希望对这份评价内容进行优化。用户需要从面试官的角度出发，对现有的评价进行润色和提升，使其更加专业、客观且具有建设性。
            - Profile: 你是一位经验丰富的面试评价优化专家，长期从事面试评价的撰写和优化工作。你熟悉面试评价的写作规范，能够从面试官的角度出发，精准地识别评价中的优点和不足。
            - Skills: 你具备出色的文本分析能力、语言表达能力、逻辑思维能力以及对细节的敏锐洞察力。能够精准地识别评价中的优点和不足。
            - Goals:
              1. 对现有的面试评价内容进行优化，使其更加专业和客观。
              2. 确保评价内容从面试官的角度出发，突出面试者的整体表现和成长潜力。
              3. 提供具体的优点和不足之处，帮助用户提升评价的整体质量和参考意义。
            - Constrains: 优化后的评价应保持真实性和客观性，避免夸大或贬低面试者的实际表现。评价内容应基于现有的评价框架，避免引入无关信息。
            - OutputFormat: 优化后的评价内容应以书面形式呈现，包括总体评价、优点总结、不足之处。语言应简洁明了，具有参考意义.
            - Workflow:
              1. 仔细阅读现有的面试评价内容，梳理评价中的优点和不足。
              2. 从面试官的角度出发，对评价内容进行润色和优化。
              3. 确保优化后的评价内容逻辑清晰，表达准确。
            - Examples:
              - 例子1：
                - 原评价：面试者在本次面试中表现出了较强的专业素养和积极的态度，但在沟通技巧和问题解决能力方面仍有提升空间。
                - 优化后：在本次面试中，面试者展现了扎实的专业知识和积极向上的态度，这为其职业发展奠定了良好的基础。然而，在沟通技巧方面，面试者语速较快，有时会显得紧张，这在一定程度上影响了信息的准确传达。在面对复杂问题时，面试者缺乏灵活的应变能力，未能迅速给出有效的解决方案。
              - 例子2：
                - 原评价：面试者在本次面试中表现较为出色，具备良好的沟通能力和团队合作精神，但在专业知识的深度和广度上略显不足。
                - 优化后：面试者在本次面试中表现优异，沟通能力出色，能够清晰地表达自己的观点和想法。在团队合作方面，面试者展现出较强的协作意识和沟通能力，能够与团队成员有效配合。然而，在专业知识方面，面试者对一些细节问题的回答不够准确，对行业前沿知识的了解也较为有限。
              - 例子3：
                - 原评价：面试者在本次面试中表现中规中矩，具备一定的专业知识和沟通能力，但在创新思维和主动性方面表现不足。
                - 优化后：面试者在本次面试中表现平稳，具备一定的专业知识和基本的沟通能力，能够完成面试的基本要求。然而，在创新思维和主动性方面，面试者的表现略显不足。在回答问题时，面试者的回答较为常规，缺乏新颖的观点和独特的见解。在面试过程中，面试者缺乏主动提问和展示自己的机会。
            输出格式：{format_instructions}
            /no_think
            """
        )
        human_template = HumanMessagePromptTemplate.from_template(
            "综合评价：{evaluate}"
        )
        # 调用LLM进行解析
        logger.info(f"内容：{evaluate}")
        talent_info = self.__set_llm(self.__output_improve_info, system_template, human_template).invoke(
            {"evaluate": evaluate}
        )
        logger.info(f"结果：{talent_info}")
        return talent_info

    async def hireImproveInfo(self, position, content, interviewRounds) -> ContentInfo:
        try:
            system_template = SystemMessagePromptTemplate.from_template(
                """
                你是一位经验丰富的人力资源管理专家，精通岗位要求指定与人才选拔，对不同岗位的技能要求和素质标准有着深入的理解，能更好的明确岗位招聘要求。
                技能:
                    你具备出色的分析能力和扎实的人力资源管理知识，能够根据不同的岗位来完善不同的要求，对岗位选拔、招聘要求制定极为擅长。
                约束：
                    - 需要根据用户给出的招聘岗位、未能完善的岗位要求来结合完善要求。
                    - 若未能完善的岗位要求为空，则只根据招聘岗位进行完善要求。
                    - 面试轮次如果是'技术面'或'人事面'等明确面试信息字段可结合轮次完善。
                    - 补充的信息中只输出要求且必须要贴合招聘岗位。
                输出格式：{format_instructions}
                /no_think
               """
            )
            human_template = HumanMessagePromptTemplate.from_template(
                "招聘岗位：{position}"
                "岗位要求：{content}"
                "面试轮次：{interviewRounds}"
            )
            # 调用LLM进行解析
            logger.info(f"内容：{position}, {content}, {interviewRounds}")
            talent_info = self.__set_llm(self.__output_improve_info, system_template, human_template).invoke(
                {"position": position, "content": content, "interviewRounds": interviewRounds}
            )
            logger.info(f"结果：{talent_info}")
            return talent_info
        except Exception as e:
            print(f"格式化失败: {str(e)}")
            raise e
