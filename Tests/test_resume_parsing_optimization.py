#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简历解析优化效果测试脚本
"""

import sys
import os
import json
import time
from typing import Dict, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Agents.TalentAgentProcessing import TalentAgentProcessing
from Utils.ResumeParsingValidator import ResumeParsingValidator
from Utils.logs.LoggingConfig import logger


class ResumeParsingTester:
    """简历解析测试器"""
    
    def __init__(self):
        self.processor = TalentAgentProcessing()
        self.validator = ResumeParsingValidator()
        
    def test_sample_resumes(self) -> Dict:
        """测试示例简历"""
        
        # 测试用例
        test_cases = [
            {
                "name": "完整简历测试",
                "content": """
                姓名：张三
                性别：男
                年龄：28岁
                手机：13812345678
                邮箱：<EMAIL>
                现居地址：北京市朝阳区
                求职意向：北京|Python开发工程师
                期望薪资：15-20K
                
                工作经历：
                2020.03-至今 ABC科技有限公司 高级Python开发工程师
                负责后端系统开发和维护，使用Django框架开发Web应用
                
                2018.06-2020.02 XYZ公司 Python开发工程师
                参与电商平台开发，负责用户模块和订单系统
                
                教育经历：
                2014.09-2018.06 北京理工大学 计算机科学与技术 本科
                
                项目经历：
                电商平台项目 2019.01-2020.01
                担任后端开发工程师，负责用户管理和订单处理模块
                技术栈：Python, Django, MySQL, Redis
                """
            },
            {
                "name": "简单简历测试",
                "content": """
                张四，女，25岁
                电话：13987654321
                Java开发，期望薪资面议
                本科毕业，计算机专业
                有2年工作经验
                """
            },
            {
                "name": "格式不规范测试",
                "content": """
                姓名张五性别男年龄30
                手机号码：138-1234-5678
                邮箱地址：<EMAIL>
                工作地点：上海求职岗位：前端开发
                薪资要求：12000-18000元
                毕业院校：复旦大学专业：软件工程学历：硕士
                工作时间：2019年3月到2023年12月
                公司名称：某互联网公司职位：前端工程师
                """
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"开始测试用例 {i}: {test_case['name']}")
            
            start_time = time.time()
            
            try:
                # 执行解析
                resume_info = self.processor._formatting(test_case['content'])
                
                processing_time = time.time() - start_time
                
                if resume_info:
                    # 生成验证报告
                    validation_report = self.validator.generate_parsing_report(resume_info)
                    
                    result = {
                        "test_case": test_case['name'],
                        "success": True,
                        "processing_time": round(processing_time, 2),
                        "validation_report": validation_report,
                        "extracted_data": {
                            "userName": resume_info.userName,
                            "sex": resume_info.sex,
                            "age": resume_info.age,
                            "phone": resume_info.phone,
                            "email": resume_info.email,
                            "jobIntent": resume_info.jobIntent,
                            "salaryExpectation": resume_info.salaryExpectation,
                            "work_experience_count": len(resume_info.tbWorkExperienceList) if resume_info.tbWorkExperienceList else 0,
                            "education_count": len(resume_info.tbEducationInfoList) if resume_info.tbEducationInfoList else 0,
                            "project_count": len(resume_info.tbProjectExperienceList) if resume_info.tbProjectExperienceList else 0
                        }
                    }
                else:
                    result = {
                        "test_case": test_case['name'],
                        "success": False,
                        "processing_time": round(processing_time, 2),
                        "error": "解析返回None"
                    }
                    
            except Exception as e:
                processing_time = time.time() - start_time
                result = {
                    "test_case": test_case['name'],
                    "success": False,
                    "processing_time": round(processing_time, 2),
                    "error": str(e)
                }
                logger.error(f"测试用例 {i} 执行失败: {str(e)}")
            
            results.append(result)
            logger.info(f"测试用例 {i} 完成，耗时: {result['processing_time']}秒")
        
        return self._generate_test_summary(results)
    
    def _generate_test_summary(self, results: List[Dict]) -> Dict:
        """生成测试摘要"""
        
        total_tests = len(results)
        successful_tests = len([r for r in results if r['success']])
        failed_tests = total_tests - successful_tests
        
        avg_processing_time = sum(r['processing_time'] for r in results) / total_tests
        
        # 计算平均完整度评分
        completeness_scores = []
        validation_errors = []
        
        for result in results:
            if result['success'] and 'validation_report' in result:
                completeness_scores.append(result['validation_report']['completeness']['score'])
                validation_errors.extend(result['validation_report']['validation']['errors'])
        
        avg_completeness = sum(completeness_scores) / len(completeness_scores) if completeness_scores else 0
        
        summary = {
            "test_summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "success_rate": round((successful_tests / total_tests) * 100, 1),
                "avg_processing_time": round(avg_processing_time, 2),
                "avg_completeness_score": round(avg_completeness, 1)
            },
            "quality_metrics": {
                "total_validation_errors": len(validation_errors),
                "common_errors": self._get_common_errors(validation_errors),
                "completeness_distribution": self._get_completeness_distribution(completeness_scores)
            },
            "detailed_results": results,
            "recommendations": self._generate_recommendations(results)
        }
        
        return summary
    
    def _get_common_errors(self, errors: List[str]) -> Dict[str, int]:
        """统计常见错误"""
        error_counts = {}
        for error in errors:
            error_type = error.split(':')[0] if ':' in error else error
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        return dict(sorted(error_counts.items(), key=lambda x: x[1], reverse=True))
    
    def _get_completeness_distribution(self, scores: List[float]) -> Dict[str, int]:
        """获取完整度分布"""
        distribution = {"优秀(90+)": 0, "良好(75-89)": 0, "一般(60-74)": 0, "较差(40-59)": 0, "很差(<40)": 0}
        
        for score in scores:
            if score >= 90:
                distribution["优秀(90+)"] += 1
            elif score >= 75:
                distribution["良好(75-89)"] += 1
            elif score >= 60:
                distribution["一般(60-74)"] += 1
            elif score >= 40:
                distribution["较差(40-59)"] += 1
            else:
                distribution["很差(<40)"] += 1
        
        return distribution
    
    def _generate_recommendations(self, results: List[Dict]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 分析成功率
        success_rate = len([r for r in results if r['success']]) / len(results)
        if success_rate < 0.8:
            recommendations.append("解析成功率较低，建议检查模型配置和提示词")
        
        # 分析处理时间
        avg_time = sum(r['processing_time'] for r in results) / len(results)
        if avg_time > 10:
            recommendations.append("处理时间较长，建议优化模型参数或考虑使用更快的模型")
        
        # 分析完整度
        successful_results = [r for r in results if r['success'] and 'validation_report' in r]
        if successful_results:
            avg_completeness = sum(r['validation_report']['completeness']['score'] for r in successful_results) / len(successful_results)
            if avg_completeness < 70:
                recommendations.append("信息提取完整度较低，建议优化提示词或使用更强的模型")
        
        # 分析常见错误
        all_errors = []
        for result in successful_results:
            all_errors.extend(result['validation_report']['validation']['errors'])
        
        if len(all_errors) > len(results):
            recommendations.append("验证错误较多，建议加强数据预处理和格式标准化")
        
        if not recommendations:
            recommendations.append("解析效果良好，可以考虑进一步优化细节")
        
        return recommendations


def main():
    """主函数"""
    print("=" * 60)
    print("简历解析优化效果测试")
    print("=" * 60)
    
    tester = ResumeParsingTester()
    
    try:
        # 执行测试
        test_results = tester.test_sample_resumes()
        
        # 输出结果
        print("\n测试摘要:")
        print("-" * 40)
        summary = test_results['test_summary']
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功数: {summary['successful_tests']}")
        print(f"失败数: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']}%")
        print(f"平均处理时间: {summary['avg_processing_time']}秒")
        print(f"平均完整度评分: {summary['avg_completeness_score']}%")
        
        print("\n质量指标:")
        print("-" * 40)
        quality = test_results['quality_metrics']
        print(f"验证错误总数: {quality['total_validation_errors']}")
        print("完整度分布:", quality['completeness_distribution'])
        
        if quality['common_errors']:
            print("常见错误:", quality['common_errors'])
        
        print("\n优化建议:")
        print("-" * 40)
        for i, rec in enumerate(test_results['recommendations'], 1):
            print(f"{i}. {rec}")
        
        # 保存详细结果到文件
        output_file = "resume_parsing_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细测试结果已保存到: {output_file}")
        
    except Exception as e:
        logger.error(f"测试执行失败: {str(e)}")
        print(f"测试执行失败: {str(e)}")


if __name__ == "__main__":
    main()
