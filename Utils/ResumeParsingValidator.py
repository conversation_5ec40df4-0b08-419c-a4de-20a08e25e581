import re
import json
from typing import Dict, List, Optional, <PERSON><PERSON>
from datetime import datetime
from Utils.logs.LoggingConfig import logger
from Models.agent.ResumeInfo import ResumeInfo


class ResumeParsingValidator:
    """简历解析结果验证和优化工具"""
    
    def __init__(self):
        self.phone_pattern = re.compile(r'^1[3-9]\d{9}$')
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        self.date_pattern = re.compile(r'^\d{4}-\d{2}$')
        
    def validate_resume_info(self, resume_info: ResumeInfo) -> Tuple[bool, List[str]]:
        """
        验证简历信息的完整性和准确性
        
        Args:
            resume_info: 解析后的简历信息对象
            
        Returns:
            Tuple[bool, List[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []
        
        # 验证必填字段
        if not resume_info.userName or resume_info.userName.strip() == "":
            errors.append("姓名字段为空或无效")
            
        # 验证手机号格式
        if resume_info.phone:
            if not self.phone_pattern.match(resume_info.phone.replace('-', '').replace(' ', '')):
                errors.append(f"手机号格式无效: {resume_info.phone}")
                
        # 验证邮箱格式
        if resume_info.email:
            if not self.email_pattern.match(resume_info.email):
                errors.append(f"邮箱格式无效: {resume_info.email}")
                
        # 验证年龄范围
        if resume_info.age is not None:
            if not (16 <= resume_info.age <= 70):
                errors.append(f"年龄超出合理范围: {resume_info.age}")
                
        # 验证工作经历日期格式
        if resume_info.tbWorkExperienceList:
            for i, work in enumerate(resume_info.tbWorkExperienceList):
                if work.startTime and not self._is_valid_date(work.startTime):
                    errors.append(f"工作经历{i+1}开始时间格式无效: {work.startTime}")
                if work.endTime and work.endTime != "至今" and not self._is_valid_date(work.endTime):
                    errors.append(f"工作经历{i+1}结束时间格式无效: {work.endTime}")
                    
        # 验证教育经历
        if resume_info.tbEducationInfoList:
            for i, edu in enumerate(resume_info.tbEducationInfoList):
                if not edu.graduationSchool or edu.graduationSchool.strip() == "":
                    errors.append(f"教育经历{i+1}学校名称为空")
                    
        # 验证薪资范围
        if resume_info.salaryRangeLowerBound is not None and resume_info.salaryRangeUpperBound is not None:
            if resume_info.salaryRangeLowerBound > resume_info.salaryRangeUpperBound:
                errors.append("期望薪资下限大于上限")
                
        return len(errors) == 0, errors
    
    def _is_valid_date(self, date_str: str) -> bool:
        """验证日期格式是否为YYYY-MM"""
        if not date_str:
            return False
        return bool(self.date_pattern.match(date_str))
    
    def suggest_improvements(self, resume_info: ResumeInfo) -> List[str]:
        """
        基于解析结果提供改进建议
        
        Args:
            resume_info: 解析后的简历信息对象
            
        Returns:
            List[str]: 改进建议列表
        """
        suggestions = []
        
        # 检查信息完整性
        if not resume_info.email:
            suggestions.append("建议补充邮箱信息")
            
        if not resume_info.currentAddress:
            suggestions.append("建议补充现居地址信息")
            
        if not resume_info.jobIntent:
            suggestions.append("建议明确求职意向")
            
        if not resume_info.tbWorkExperienceList or len(resume_info.tbWorkExperienceList) == 0:
            suggestions.append("缺少工作经历信息，建议补充")
            
        if not resume_info.tbEducationInfoList or len(resume_info.tbEducationInfoList) == 0:
            suggestions.append("缺少教育经历信息，建议补充")
            
        # 检查工作经历质量
        if resume_info.tbWorkExperienceList:
            for work in resume_info.tbWorkExperienceList:
                if not work.workIntroduction or len(work.workIntroduction.strip()) < 20:
                    suggestions.append("工作经历描述过于简单，建议详细描述工作内容")
                    break
                    
        # 检查技能信息
        if not resume_info.skillList or len(resume_info.skillList) == 0:
            suggestions.append("建议补充技能列表信息")
            
        return suggestions
    
    def calculate_completeness_score(self, resume_info: ResumeInfo) -> float:
        """
        计算简历信息完整度评分
        
        Args:
            resume_info: 解析后的简历信息对象
            
        Returns:
            float: 完整度评分 (0-100)
        """
        total_fields = 0
        filled_fields = 0
        
        # 基本信息字段 (权重较高)
        basic_fields = [
            resume_info.userName, resume_info.phone, resume_info.email,
            resume_info.age, resume_info.currentAddress, resume_info.jobIntent
        ]
        
        for field in basic_fields:
            total_fields += 2  # 基本信息权重为2
            if field and str(field).strip():
                filled_fields += 2
                
        # 经历信息字段
        experience_fields = [
            resume_info.tbWorkExperienceList,
            resume_info.tbEducationInfoList,
            resume_info.tbProjectExperienceList
        ]
        
        for field in experience_fields:
            total_fields += 3  # 经历信息权重为3
            if field and len(field) > 0:
                filled_fields += 3
                
        # 其他信息字段
        other_fields = [
            resume_info.education, resume_info.skillList,
            resume_info.salaryExpectation, resume_info.introduction
        ]
        
        for field in other_fields:
            total_fields += 1  # 其他信息权重为1
            if field and (isinstance(field, list) and len(field) > 0 or 
                         isinstance(field, str) and field.strip()):
                filled_fields += 1
                
        if total_fields == 0:
            return 0.0
            
        return round((filled_fields / total_fields) * 100, 1)
    
    def generate_parsing_report(self, resume_info: ResumeInfo) -> Dict:
        """
        生成简历解析报告
        
        Args:
            resume_info: 解析后的简历信息对象
            
        Returns:
            Dict: 解析报告
        """
        is_valid, errors = self.validate_resume_info(resume_info)
        suggestions = self.suggest_improvements(resume_info)
        completeness = self.calculate_completeness_score(resume_info)
        
        report = {
            "validation": {
                "is_valid": is_valid,
                "errors": errors,
                "error_count": len(errors)
            },
            "completeness": {
                "score": completeness,
                "level": self._get_completeness_level(completeness)
            },
            "suggestions": suggestions,
            "summary": {
                "total_suggestions": len(suggestions),
                "critical_issues": len([e for e in errors if "无效" in e or "为空" in e]),
                "recommendation": self._get_overall_recommendation(completeness, len(errors))
            }
        }
        
        return report
    
    def _get_completeness_level(self, score: float) -> str:
        """根据完整度评分获取等级"""
        if score >= 90:
            return "优秀"
        elif score >= 75:
            return "良好"
        elif score >= 60:
            return "一般"
        elif score >= 40:
            return "较差"
        else:
            return "很差"
    
    def _get_overall_recommendation(self, completeness: float, error_count: int) -> str:
        """获取总体建议"""
        if error_count > 3:
            return "解析质量较差，建议检查原始简历格式或优化解析模型"
        elif completeness < 50:
            return "信息完整度较低，建议补充关键信息"
        elif completeness < 75:
            return "信息基本完整，建议补充部分细节信息"
        else:
            return "解析质量良好，信息较为完整"
