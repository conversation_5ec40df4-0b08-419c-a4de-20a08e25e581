from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


# 工作经历
class WorkExperience(BaseModel):
    def __init__(self, **data):
        super().__init__(**data)
        self._timeConvert(**data)

    companyName: Optional[str] = Field(description="公司名称")
    position: Optional[str] = Field(description="职位")
    # resignationReason: Optional[str] = Field(description="离职原因(没有相关信息可不填)")
    workIntroduction: Optional[str] = Field(description="任职期间的工作内容")
    startTime: Optional[str] = Field(description="起始年份")
    endTime: Optional[str] = Field(description="截至年份")

    # 格式转化
    def _timeConvert(self, **data):
        if self.startTime is not None:
            if '.' in self.startTime:
                self.startTime = self.startTime.replace('.', '-')
            if '/' in self.startTime:
                self.startTime = self.startTime.replace('/', '-')
            if len(self.startTime) == 6:
                self.startTime = self.startTime[:4] + '-' + self.startTime[4:]
        if self.endTime is not None:
            if self.endTime == "至今":
                self.endTime = datetime.now().strftime("%Y-%m")
            if '.' in self.endTime:
                self.endTime = self.endTime.replace('.', '-')
            if '/' in self.endTime:
                self.endTime = self.endTime.replace('/', '-')
            if len(self.endTime) == 6:
                self.endTime = self.endTime[:4] + '-' + self.endTime[4:]


# 项目经历
class ProjectExperience(BaseModel):
    def __init__(self, **data):
        super().__init__(**data)
        self._timeConvert(**data)

    projectName: Optional[str] = Field(description="项目名称")
    jobTitle: Optional[str] = Field(description="在项目担任职位")
    projectDescription: Optional[str] = Field(description="项目描述")
    responsibleContent: Optional[str] = Field(description="职责内容")
    startTime: Optional[str] = Field(description="项目起始日期")
    endTime: Optional[str] = Field(description="项目截至日期")

    # 格式转化
    def _timeConvert(self, **data):
        if self.startTime is not None:
            if '.' in self.startTime:
                self.startTime = self.startTime.replace('.', '-')
            if '/' in self.startTime:
                self.startTime = self.startTime.replace('/', '-')
            if len(self.startTime) == 6:
                self.startTime = self.startTime[:4] + '-' + self.startTime[4:]
        if self.endTime is not None:
            if self.endTime == "至今":
                self.endTime = datetime.now().strftime("%Y-%m")
            if '.' in self.endTime:
                self.endTime = self.endTime.replace('.', '-')
            if '/' in self.endTime:
                self.endTime = self.endTime.replace('/', '-')
            if len(self.endTime) == 6:
                self.endTime = self.endTime[:4] + '-' + self.endTime[4:]


# 教育信息
class EducationInfo(BaseModel):
    graduationSchool: Optional[str] = Field(description="学校名称")
    major: Optional[str] = Field(description="专业")
    educationLevel: Optional[str] = Field(description="学历")
    introduction: Optional[str] = Field(description="在校经历")


# 给Java端传值的对象
class ResumeInfo(BaseModel):
    fileId: Optional[int] = Field(description="简历Id", default=None)
    jobId: Optional[int] = Field(description="任务Id", default=None)
    userName: Optional[str] = Field(description="姓名", default=None)
    sex: Optional[str] = Field(description="性别", default="2")
    age: Optional[int] = Field(description="年龄", default=None)
    email: Optional[str] = Field(description="邮箱", default=None)
    ethnicity: Optional[str] = Field(description="民族", default=None)
    marriageStatus: Optional[str] = Field(description="婚姻状态", default=None)
    workStatus: Optional[str] = Field(description="工作状态（在职或离职）", default=None)
    position: Optional[str] = Field(description="职位", default=None)
    phone: Optional[str] = Field(description="手机号码", default=None)
    avatar: Optional[str] = Field(description="照片地址", default=None)
    currentAddress: Optional[str] = Field(description="当前地址", default=None)
    politicalStatus: Optional[str] = Field(description="政治面貌", default=None)
    introduction: Optional[str] = Field(description="个人简介", default=None)
    foreignProficiency: Optional[str] = Field(description="外语水平", default=None)
    professionalLevel: Optional[str] = Field(description="计算机水平", default=None)
    jobIntent: Optional[str] = Field(description="求职意向（期望岗位名称）", default=None)
    salaryExpectation: Optional[str] = Field(description="薪资期望", default=None)
    education: Optional[str] = Field(description="学历", default=None)
    major: Optional[str] = Field(description="专业", default=None)
    schoolName: Optional[str] = Field(description="毕业学校", default=None)
    yearsOfExperience: Optional[int] = Field(description="工作经验（工作年限）", default=None)
    skillList: Optional[List[str]] = Field(description="技能列表", default=None)
    tbWorkExperienceList: Optional[List[WorkExperience]] = Field(description="工作经历列表(入职过的公司)", default=None)
    tbEducationInfoList: Optional[List[EducationInfo]] = Field(description="教育经历列表", default=None)
    tbProjectExperienceList: Optional[List[ProjectExperience]] = Field(description="项目经历列表", default=None)
    minimumEducationScore: Optional[float] = Field(description="教育经历得分", default=0)
    educationEvaluation: Optional[str] = Field(description="教育经历评价", default=None)
    workExperienceScore: Optional[float] = Field(description="工作经验得分", default=0)
    workExperienceEvaluation: Optional[str] = Field(description="工作经验评价", default=None)
    jobHoppingRateScore: Optional[float] = Field(description="工作经历得分", default=0)
    jobHoppingRateEvaluation: Optional[str] = Field(description="工作经验评价", default=None)
    salaryRangeScore: Optional[float] = Field(description="期望薪资得分", default=0)
    salaryRangeEvaluation: Optional[str] = Field(description="期望薪资评价", default=None)
    totalScore: Optional[float] = Field(description="总分(保留小数点后两位)", default=0)
    comprehensiveEvaluation: Optional[str] = Field(description="总体评价", default=None)
    salaryRangeLowerBound: Optional[int] = Field(description="薪资范围下限(单位K，1000 = 1)", default=None)
    salaryRangeUpperBound: Optional[int] = Field(description="薪资范围上限(单位K，1000 = 1)", default=None)
    certificate: Optional[str] = Field(description="证书信息(多段使用 ',' 拼接)", default=None)
    matchingDegree: Optional[int] = Field(description="匹配度（整数）", default=0)
