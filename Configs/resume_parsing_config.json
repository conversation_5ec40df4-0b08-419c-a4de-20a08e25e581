{"resume_parsing": {"model_settings": {"temperature": 0.1, "max_tokens": 4000, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}, "parsing_strategy": {"use_segmentation": true, "segmentation_method": "simple", "parallel_processing": true, "retry_on_failure": true, "max_retries": 2}, "validation": {"required_fields": ["userName", "phone", "email"], "date_format_validation": true, "salary_format_validation": true, "phone_format_validation": true, "email_format_validation": true}, "optimization_tips": {"model_recommendations": ["对于qwen3:1.7b模型，建议升级到qwen2.5:7b或更大模型", "考虑使用专门的信息抽取模型如GLM-4或ChatGLM3", "如果必须使用小模型，建议分步骤处理而非一次性提取所有信息"], "prompt_engineering": ["使用更具体的示例和格式说明", "采用Chain-of-Thought提示技术", "为每个字段提供明确的提取规则", "使用结构化的输出格式约束"], "processing_optimization": ["降低temperature到0.1以提高一致性", "增加max_tokens以避免输出截断", "使用多轮对话进行信息确认", "实施输出验证和错误重试机制"]}}}