from typing import List

import jieba.analyse
from langchain_community.retrievers import BM25Retriever
from langchain_core.callbacks import CallbackManagerForRetrieverRun
from langchain_core.documents import Document

from Configs.Config import SysConfig
from Services.KBTrunkVectorService.stopwords import STOPWORDS
from Services.SqlServer.KBFileTrunkInfoService import KBFileTrunkInfoService
from Utils.logs.LoggingConfig import logger

jieba.analyse.default_tfidf.stop_words = STOPWORDS
# 添加日志控制
import jieba
import logging

jieba.setLogLevel(logging.INFO)


# 提取关键词
def chinese_keywords(text: str) -> list[str]:
    try:
        tfidf_words = jieba.analyse.extract_tags(text, topK=20, withWeight=False)
        # textrank_words = jieba.analyse.textrank(text, topK=20, withWeight=False)
        # keywords = list(set(tfidf_words + textrank_words))
        return tfidf_words
    except Exception as e:
        logger.error(f"Error in chinese_preprocess: {e}")
        return []


# 中文分词逻辑
def chinese_cup(text: str) -> list[str]:
    try:
        seg_list = jieba.cut(text)
        return list(seg_list)
    except Exception as e:
        logger.error(f"Error in chinese_preprocess: {e}")
        return []


_bm25_store_retriever: dict[str, BM25Retriever] = {}


def query_bm25_retriever(query: str, file_ids: list[str]) -> list[Document]:
    """获取或创建 BM25Retriever"""
    bm25_retriever = get_bm25_retriever(file_ids=file_ids)

    return bm25_retriever.invoke(query)


def get_bm25_retriever(file_ids: list[str]) -> BM25Retriever | None:
    k = SysConfig["retrievers"]["vector"]["k"]
    key = "_".join([file_id for file_id in file_ids])
    bm25_retriever = _bm25_store_retriever.get(key)
    if not bm25_retriever:
        bm25_retriever = CustomBM25Retriever.create_bm25retriever(file_ids, k)
        if bm25_retriever:
            _bm25_store_retriever[key] = bm25_retriever
            return bm25_retriever
    return bm25_retriever


class CustomBM25Retriever(BM25Retriever):
    score_threshold: float = 4.0  # 新增阈值参数

    @staticmethod
    def create_bm25retriever(file_ids: list[str], k: int) -> BM25Retriever | None:
        trunk_docs = KBFileTrunkInfoService().select_by_file_ids(file_ids)
        docs: list[Document] = []
        for trunk in trunk_docs:
            doc = KBFileTrunkInfoService.transform_to_document(trunk)
            doc.metadata["keywords"] = trunk.keywords
            docs.append(doc)
        if len(docs) > 0:
            bm25 = CustomBM25Retriever.from_documents(documents=docs, preprocess_func=chinese_cup)
            bm25.k = k
            return bm25
        else:
            return None

    def _search_by_keywords(self, query: str) -> List[Document]:
        # 提取关键词
        keywords = chinese_keywords(query)
        if not keywords:
            keywords = [query]
        # 构建查询条件
        for keyword in keywords:
            # 构建查询条件
            for doc in self.docs:
                # 如果关键词在文档的关键词列表中，
                if keyword in doc.metadata.get("keywords", ""):
                    doc.metadata["keyword_count"] = doc.metadata.get("keyword_count", 0) + 1

        # 根据关键词数量排序
        sorted_docs = sorted(self.docs, key=lambda x: x.metadata.get("keyword_count", 0), reverse=True)

        sorted_docs = sorted_docs[:1]

        return [item for item in sorted_docs if item.metadata.get("keyword_count", 0) > 0]

    def _get_relevant_documents(self, query: str, *, run_manager: CallbackManagerForRetrieverRun) -> List[Document]:
        processed_query = self.preprocess_func(query)
        # 获取所有文档的BM25得分
        scores = self.vectorizer.get_scores(processed_query)
        # 将文档与得分绑定并按得分降序排序
        scored_docs = sorted(zip(scores, self.docs), key=lambda x: x[0], reverse=True)
        #  过滤掉得分为0的文档（严格无匹配场景）
        filtered_docs = []
        for score, doc in scored_docs:
            if score < self.score_threshold:
                break
            filtered_docs.append(doc)

        keywords_docs = self._search_by_keywords(query)

        # 合并结果并去重（基于文档id）
        seen_ids = set()
        unique_docs = []
        for doc in keywords_docs + filtered_docs:
            if doc.id not in seen_ids:
                seen_ids.add(doc.id)
                unique_docs.append(doc)

        # 仅返回前k个有效结果
        return unique_docs[:self.k]
